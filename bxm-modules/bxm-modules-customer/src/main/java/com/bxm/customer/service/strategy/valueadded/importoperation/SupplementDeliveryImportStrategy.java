package com.bxm.customer.service.strategy.valueadded.importoperation;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.ValueAddedFile;
import com.bxm.customer.domain.dto.valueAdded.BatchImportOperationRequest;
import com.bxm.customer.domain.dto.valueAdded.BatchImportOperationResult;
import com.bxm.customer.domain.dto.valueAdded.BatchOperationErrorDTO;
import com.bxm.customer.domain.dto.valueAdded.BaseImportExcelDTO;
import com.bxm.customer.domain.dto.valueAdded.ImportValidationResult;
import com.bxm.customer.domain.dto.valueAdded.SupplementDeliveryImportExcelDTO;
import com.bxm.customer.domain.dto.valueAdded.TemplateParseResult;
import com.bxm.customer.helper.ValueAddedImportValidationHelper;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.domain.enums.ValueAddedBatchImportOperationType;
import com.bxm.customer.service.IValueAddedFileService;
import com.bxm.customer.service.strategy.ImportOperationStrategy;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 补充交付附件导入策略实现
 *
 * 处理补充交付附件操作的导入逻辑：
 * 1. 不修改交付单状态
 * 2. 仅将解压后的文件保存到value_added_file表
 * 3. 支持多种状态的交付单进行附件补充
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Component
public class SupplementDeliveryImportStrategy implements ImportOperationStrategy {

    @Autowired
    private IValueAddedFileService fileService;

    @Autowired
    private ValueAddedImportValidationHelper valueAddedImportValidationHelper;

    @Override
    public ValueAddedBatchImportOperationType getSupportedOperationType() {
        return ValueAddedBatchImportOperationType.SUPPLEMENT_DELIVERY;
    }

    @Override
    public BatchImportOperationResult executeImport(
            List<ValueAddedDeliveryOrder> orders,
            BatchImportOperationRequest request,
            Map<String, Object> templateData,
            Map<String, String> extractedFiles) {

        log.info("Starting supplement delivery import operation, order count: {}", orders.size());

        LocalDateTime startTime = LocalDateTime.now();
        List<String> successOrderNos = new ArrayList<>();
        List<BatchOperationErrorDTO> errors = new ArrayList<>();

        // 验证模板数据
        try {
            validateTemplateData(templateData, request.getDeliveryOrderNoList());
        } catch (Exception e) {
            log.error("Template data validation failed: {}", e.getMessage());
            throw new IllegalArgumentException("Template data validation failed: " + e.getMessage());
        }

        // 处理每个交付单
        for (ValueAddedDeliveryOrder order : orders) {
            try {
                // 验证交付单状态（补充附件操作相对宽松）
                validateOrderStatus(order);

                // Supplement delivery operation does not modify status, only process file saving
                int savedFileCount = 0;
                if (extractedFiles != null && !extractedFiles.isEmpty()) {
                    savedFileCount = processFileSaving(order, extractedFiles, request);
                    log.info("Delivery order {} supplement attachment count: {}", order.getDeliveryOrderNo(), savedFileCount);
                }

                // Even without files, it's considered successful (may just update template info)
                successOrderNos.add(order.getDeliveryOrderNo());

            } catch (Exception e) {
                log.warn("Delivery order {} supplement attachment failed: {}", order.getDeliveryOrderNo(), e.getMessage());
                errors.add(BatchOperationErrorDTO.builder()
                        .deliveryOrderNo(order.getDeliveryOrderNo())
                        .customerName(order.getCustomerName())
                        .build());
            }
        }

        LocalDateTime endTime = LocalDateTime.now();
        long processingTime = java.time.Duration.between(startTime, endTime).toMillis();

        return BatchImportOperationResult.builder()
                .operationDescription(getOperationDescription())
                .totalCount(orders.size())
                .successCount(successOrderNos.size())
                .errorCount(errors.size())
                .successOrderNos(successOrderNos)
                .errors(errors)
                .startTime(startTime)
                .endTime(endTime)
                .processingTimeMs(processingTime)
                .build();
    }

    @Override
    public void validateOrderStatus(ValueAddedDeliveryOrder order) {
        String currentStatus = order.getStatus();
        ValueAddedDeliveryOrderStatus status = ValueAddedDeliveryOrderStatus.getByCode(currentStatus);

        if (status == null) {
            throw new IllegalArgumentException(
                    String.format("Delivery order %s status invalid: %s", order.getDeliveryOrderNo(), currentStatus)
            );
        }

        // Supplement delivery operation allows more flexible status, but not draft status
        if (ValueAddedDeliveryOrderStatus.DRAFT.equals(status)) {
            throw new IllegalArgumentException(
                    String.format("Delivery order %s status does not allow supplement attachment operation, current status: %s",
                            order.getDeliveryOrderNo(), status.getDescription())
            );
        }

        log.debug("Delivery order {} status validation passed, current status: {}", order.getDeliveryOrderNo(), status.getDescription());
    }

    @Override
    public String getTargetStatus(String currentStatus) {
        // 补充附件操作不修改状态
        return null;
    }



    @Override
    public int processFileSaving(
            ValueAddedDeliveryOrder order,
            Map<String, String> extractedFiles,
            BatchImportOperationRequest request) {

        int savedCount = 0;
        String deliveryOrderNo = order.getDeliveryOrderNo();

        for (Map.Entry<String, String> entry : extractedFiles.entrySet()) {
            try {
                String fileName = entry.getKey();
                String filePath = entry.getValue();

                // Supplement delivery operation: save all extracted files
                // Can perform more precise matching based on file name rules
                if (isFileRelatedToOrder(fileName, deliveryOrderNo)) {
                    ValueAddedFile file = new ValueAddedFile();
                    file.setDeliveryOrderNo(deliveryOrderNo);
                    file.setFileName(fileName);
                    file.setFileUrl(filePath);
                    file.setFileType(1); // 1-交付材料附件
                    file.setStatus(1); // 1-处理完成
                    file.setIsDel(false);
                    file.setRemark("批量补充附件导入");
                    file.setCreateBy(SecurityUtils.getUserId().toString());

                    boolean saved = fileService.save(file);
                    if (saved) {
                        savedCount++;
                        log.debug("Supplement attachment saved successfully: {} -> {}", fileName, deliveryOrderNo);
                    }
                }
            } catch (Exception e) {
                log.warn("Supplement attachment save failed: {}, error: {}", entry.getKey(), e.getMessage());
            }
        }

        return savedCount;
    }

    /**
     * 判断文件是否与交付单相关
     * 补充附件操作的文件匹配规则可能更灵活
     */
    private boolean isFileRelatedToOrder(String fileName, String deliveryOrderNo) {
        if (fileName == null || deliveryOrderNo == null) {
            return false;
        }

        // Supplement delivery file matching rules:
        // 1. File name contains delivery order number
        // 2. Or file name contains enterprise credit code (need to get from delivery order)
        // 3. Or match according to folder structure

        String upperFileName = fileName.toUpperCase();
        String upperOrderNo = deliveryOrderNo.toUpperCase();

        return upperFileName.contains(upperOrderNo);
    }

    @Override
    public TemplateParseResult parseTemplateFile(MultipartFile templateFile) throws Exception {
        log.info("开始解析补充交付附件操作Excel模板文件: {}", templateFile.getOriginalFilename());

        try {
            // 使用ExcelUtil解析Excel文件为SupplementDeliveryImportExcelDTO列表
            ExcelUtil<SupplementDeliveryImportExcelDTO> excelUtil = new ExcelUtil<>(SupplementDeliveryImportExcelDTO.class);
            List<SupplementDeliveryImportExcelDTO> dataList = excelUtil.importExcel(templateFile.getInputStream());

            // 设置行号用于错误定位
            for (int i = 0; i < dataList.size(); i++) {
                dataList.get(i).setRowNumber(i + 2); // Excel从第2行开始是数据行
            }

            // 使用新的基础校验逻辑
            ImportValidationResult basicValidationResult = valueAddedImportValidationHelper.performBasicValidation(dataList);

            if (!basicValidationResult.getIsValid()) {
                // 基础校验失败，返回错误结果
                return TemplateParseResult.failure(basicValidationResult.getErrors());
            }

            // 基础校验通过，补充交付附件操作暂时不需要特殊校验，直接返回成功结果
            return TemplateParseResult.success(basicValidationResult.getValidData());

        } catch (Exception e) {
            log.error("补充交付附件操作Excel模板文件解析失败: {}", e.getMessage(), e);
            throw new Exception("补充交付附件操作Excel模板文件解析失败: " + e.getMessage(), e);
        }
    }
}
