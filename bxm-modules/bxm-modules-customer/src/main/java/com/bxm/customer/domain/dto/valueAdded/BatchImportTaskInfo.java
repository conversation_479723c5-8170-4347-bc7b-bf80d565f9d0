package com.bxm.customer.domain.dto.valueAdded;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 批量导入任务信息存储DTO
 *
 * 用于在Redis中存储批量导入任务信息
 * 包含批次号、状态和统计数据
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchImportTaskInfo {

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 任务状态：PENDING-待处理、PROCESSING-处理中、COMPLETED-已完成、FAILED-失败
     */
    private String status;

    /**
     * 总数据量
     */
    private Integer total;

    /**
     * 成功数量
     */
    private Integer successCnt;

    /**
     * 失败数量
     */
    private Integer failCnt;

    /**
     * 标记任务开始
     */
    public void markAsStarted() {
        this.status = "PROCESSING";
    }

    /**
     * 标记任务完成
     */
    public void markAsCompleted() {
        this.status = "COMPLETED";
    }

    /**
     * 标记任务失败
     */
    public void markAsFailed() {
        this.status = "FAILED";
    }
}
