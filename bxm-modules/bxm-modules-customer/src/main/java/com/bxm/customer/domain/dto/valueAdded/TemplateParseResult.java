package com.bxm.customer.domain.dto.valueAdded;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 模板解析结果DTO
 *
 * 包含Excel模板解析的成功数据和校验错误信息
 * 用于在解析阶段同时返回成功和失败的记录
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("模板解析结果DTO")
public class TemplateParseResult {

    /**
     * 解析成功的数据列表
     */
    @ApiModelProperty(value = "解析成功的数据列表")
    private List<? extends BaseImportExcelDTO> successData;

    /**
     * 校验错误列表
     */
    @ApiModelProperty(value = "校验错误列表")
    private List<ImportValidationErrorDTO> validationErrors;

    /**
     * 解析成功的记录数量
     */
    @ApiModelProperty(value = "解析成功的记录数量")
    private Integer successCount;

    /**
     * 校验失败的记录数量
     */
    @ApiModelProperty(value = "校验失败的记录数量")
    private Integer errorCount;

    /**
     * 是否有校验错误
     */
    @ApiModelProperty(value = "是否有校验错误")
    private Boolean hasValidationErrors;

    /**
     * 批次号（当有错误时生成）
     */
    @ApiModelProperty(value = "批次号")
    private String batchNo;

    /**
     * 创建成功结果
     */
    public static TemplateParseResult success(List<? extends BaseImportExcelDTO> data) {
        return TemplateParseResult.builder()
                .successData(data)
                .validationErrors(new ArrayList<>())
                .successCount(data.size())
                .errorCount(0)
                .hasValidationErrors(false)
                .build();
    }

    /**
     * 创建包含错误的结果
     */
    public static TemplateParseResult withErrors(List<? extends BaseImportExcelDTO> successData,
                                               List<ImportValidationErrorDTO> errors,
                                               String batchNo) {
        return TemplateParseResult.builder()
                .successData(successData)
                .validationErrors(errors)
                .successCount(successData.size())
                .errorCount(errors.size())
                .hasValidationErrors(!errors.isEmpty())
                .batchNo(batchNo)
                .build();
    }

    /**
     * 创建失败结果（仅包含错误，无成功数据）
     */
    public static TemplateParseResult failure(List<ImportValidationErrorDTO> errors) {
        String batchNo = java.util.UUID.randomUUID().toString().replaceAll("-", "");
        return TemplateParseResult.builder()
                .successData(new ArrayList<>())
                .validationErrors(errors)
                .successCount(0)
                .errorCount(errors.size())
                .hasValidationErrors(true)
                .batchNo(batchNo)
                .build();
    }

    /**
     * 创建部分成功结果
     */
    public static TemplateParseResult partial(List<? extends BaseImportExcelDTO> successData,
                                            List<ImportValidationErrorDTO> errors) {
        String batchNo = java.util.UUID.randomUUID().toString().replaceAll("-", "");
        return TemplateParseResult.builder()
                .successData(successData)
                .validationErrors(errors)
                .successCount(successData.size())
                .errorCount(errors.size())
                .hasValidationErrors(!errors.isEmpty())
                .batchNo(batchNo)
                .build();
    }
}
