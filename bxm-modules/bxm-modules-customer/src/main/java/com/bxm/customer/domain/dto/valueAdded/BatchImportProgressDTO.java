package com.bxm.customer.domain.dto.valueAdded;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 批量导入进度查询响应DTO
 *
 * 用于返回批量导入任务的执行进度信息
 * 包含任务状态、数据统计、时间信息等
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("批量导入进度查询响应DTO")
public class BatchImportProgressDTO {

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号", example = "abc123def456")
    private String batchNo;

    /**
     * 任务状态：PENDING-待处理、PROCESSING-处理中、COMPLETED-已完成、FAILED-失败
     */
    @ApiModelProperty(value = "任务状态", allowableValues = "PENDING,PROCESSING,COMPLETED,FAILED", example = "PROCESSING")
    private String status;

    /**
     * 总数据量
     */
    @ApiModelProperty(value = "总数据量", example = "100")
    private Integer totalCount;

    /**
     * 成功数量
     */
    @ApiModelProperty(value = "成功数量", example = "95")
    private Integer successCount;

    /**
     * 异常数量
     */
    @ApiModelProperty(value = "异常数量", example = "5")
    private Integer errorCount;

    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        if (totalCount == null || totalCount == 0) {
            return 0.0;
        }
        return (double) (successCount != null ? successCount : 0) / totalCount * 100;
    }

    /**
     * 判断任务是否完成
     */
    public boolean isCompleted() {
        return "COMPLETED".equals(status) || "FAILED".equals(status);
    }

    /**
     * 判断任务是否成功
     */
    public boolean isSuccess() {
        return "COMPLETED".equals(status);
    }

    /**
     * 判断是否有错误
     */
    public boolean hasErrors() {
        return errorCount != null && errorCount > 0;
    }
}
